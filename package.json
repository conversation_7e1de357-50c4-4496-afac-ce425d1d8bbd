{"name": "alden", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate"}, "dependencies": {"@clerk/nextjs": "^6.31.9", "@neondatabase/serverless": "^1.0.1", "drizzle-orm": "^0.44.5", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.2", "drizzle-kit": "^0.31.4", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67"}