import Stripe from 'stripe';
import { createHash } from 'crypto';
import { calculateSubscriptionMRR, getSubscriptionMetadata } from './stripe';

export type MRREventType = 'start' | 'upgrade' | 'downgrade' | 'cancel';

export interface MRREvent {
  uniqueSourceKey: string;
  subscriptionId: string;
  srcEventType: MRREventType;
  mrrDeltaCents: number;
  effectiveDate: string; // YYYY-MM-DD
  stripeEventId?: string;
  customerId: string;
  productId?: string;
  priceId?: string;
}

/**
 * Generate a unique source key for MRR events
 */
export function generateUniqueSourceKey(
  subscriptionId: string,
  eventType: MRREventType,
  effectiveDate: string,
  additionalData?: string
): string {
  const data = `${subscriptionId}:${eventType}:${effectiveDate}${additionalData ? `:${additionalData}` : ''}`;
  return createHash('sha256').update(data).digest('hex');
}

/**
 * Convert timestamp to YYYY-MM-DD format
 */
export function formatEffectiveDate(timestamp: number): string {
  return new Date(timestamp * 1000).toISOString().split('T')[0];
}

/**
 * Calculate MRR events for a subscription based on its current state and history
 */
export function calculateMRREvents(
  subscription: Stripe.Subscription,
  events: Stripe.Event[] = []
): MRREvent[] {
  const mrrEvents: MRREvent[] = [];
  const metadata = getSubscriptionMetadata(subscription);

  // Sort events by created timestamp
  const sortedEvents = events.sort((a, b) => a.created - b.created);

  // Track MRR changes over time
  let previousMRR = 0;
  let hasProcessedCreation = false;

  for (const event of sortedEvents) {
    const eventData = event.data.object as any;
    const effectiveDate = formatEffectiveDate(event.created);

    switch (event.type) {
      case 'customer.subscription.created': {
        if (eventData.id === subscription.id && !hasProcessedCreation) {
          const currentMRR = calculateSubscriptionMRR(eventData);
          
          mrrEvents.push({
            uniqueSourceKey: generateUniqueSourceKey(
              subscription.id,
              'start',
              effectiveDate,
              event.id
            ),
            subscriptionId: subscription.id,
            srcEventType: 'start',
            mrrDeltaCents: currentMRR,
            effectiveDate,
            stripeEventId: event.id,
            ...metadata,
          });

          previousMRR = currentMRR;
          hasProcessedCreation = true;
        }
        break;
      }

      case 'customer.subscription.updated': {
        if (eventData.id === subscription.id) {
          const currentMRR = calculateSubscriptionMRR(eventData);
          const mrrDelta = currentMRR - previousMRR;

          if (mrrDelta !== 0) {
            const eventType: MRREventType = mrrDelta > 0 ? 'upgrade' : 'downgrade';

            mrrEvents.push({
              uniqueSourceKey: generateUniqueSourceKey(
                subscription.id,
                eventType,
                effectiveDate,
                event.id
              ),
              subscriptionId: subscription.id,
              srcEventType: eventType,
              mrrDeltaCents: mrrDelta,
              effectiveDate,
              stripeEventId: event.id,
              ...metadata,
            });

            previousMRR = currentMRR;
          }
        }
        break;
      }

      case 'customer.subscription.deleted': {
        if (eventData.id === subscription.id) {
          mrrEvents.push({
            uniqueSourceKey: generateUniqueSourceKey(
              subscription.id,
              'cancel',
              effectiveDate,
              event.id
            ),
            subscriptionId: subscription.id,
            srcEventType: 'cancel',
            mrrDeltaCents: -previousMRR,
            effectiveDate,
            stripeEventId: event.id,
            ...metadata,
          });

          previousMRR = 0;
        }
        break;
      }
    }
  }

  // If no creation event was found in the events, create one based on subscription start date
  if (!hasProcessedCreation && subscription.status !== 'canceled') {
    const currentMRR = calculateSubscriptionMRR(subscription);
    const effectiveDate = formatEffectiveDate(subscription.created);

    mrrEvents.unshift({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'start',
        effectiveDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'start',
      mrrDeltaCents: currentMRR,
      effectiveDate,
      ...metadata,
    });
  }

  // If subscription is canceled and we haven't processed the cancellation
  if (subscription.status === 'canceled' && subscription.canceled_at) {
    const cancelDate = formatEffectiveDate(subscription.canceled_at);
    const hasCancelEvent = mrrEvents.some(
      event => event.srcEventType === 'cancel' && event.effectiveDate === cancelDate
    );

    if (!hasCancelEvent) {
      const currentMRR = mrrEvents.reduce((sum, event) => sum + event.mrrDeltaCents, 0);
      
      mrrEvents.push({
        uniqueSourceKey: generateUniqueSourceKey(
          subscription.id,
          'cancel',
          cancelDate
        ),
        subscriptionId: subscription.id,
        srcEventType: 'cancel',
        mrrDeltaCents: -currentMRR,
        effectiveDate: cancelDate,
        ...metadata,
      });
    }
  }

  return mrrEvents;
}

/**
 * Calculate MRR events for a subscription without historical events
 * This is useful for initial sync when we only have the current subscription state
 */
export function calculateMRREventsFromSubscription(
  subscription: Stripe.Subscription
): MRREvent[] {
  const metadata = getSubscriptionMetadata(subscription);
  const mrrEvents: MRREvent[] = [];

  // Create start event
  if (subscription.status !== 'canceled') {
    const currentMRR = calculateSubscriptionMRR(subscription);
    const effectiveDate = formatEffectiveDate(subscription.created);

    mrrEvents.push({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'start',
        effectiveDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'start',
      mrrDeltaCents: currentMRR,
      effectiveDate,
      ...metadata,
    });
  }

  // Create cancel event if subscription is canceled
  if (subscription.status === 'canceled' && subscription.canceled_at) {
    const currentMRR = calculateSubscriptionMRR(subscription);
    const cancelDate = formatEffectiveDate(subscription.canceled_at);

    mrrEvents.push({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'cancel',
        cancelDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'cancel',
      mrrDeltaCents: -currentMRR,
      effectiveDate: cancelDate,
      ...metadata,
    });
  }

  return mrrEvents;
}
