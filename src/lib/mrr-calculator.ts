import { createHash } from 'crypto';
import Stripe from 'stripe';
import { calculateSubscriptionMRR, getSubscriptionMetadata } from './stripe';

export type MRREventType = 'start' | 'upgrade' | 'downgrade' | 'cancel';

export interface MRREvent {
  uniqueSourceKey: string;
  subscriptionId: string;
  srcEventType: MRREventType;
  mrrDeltaCents: number;
  effectiveDate: string; // YYYY-MM-DD
  stripeEventId?: string;
  customerId: string;
  productId?: string;
  priceId?: string;
}

/**
 * Generate a unique source key for MRR events
 */
export function generateUniqueSourceKey(
  subscriptionId: string,
  eventType: MRREventType,
  effectiveDate: string,
  additionalData?: string
): string {
  const data = `${subscriptionId}:${eventType}:${effectiveDate}${additionalData ? `:${additionalData}` : ''}`;
  return createHash('sha256').update(data).digest('hex');
}

/**
 * Convert timestamp to YYYY-MM-DD format
 */
export function formatEffectiveDate(timestamp: number): string {
  return new Date(timestamp * 1000).toISOString().split('T')[0];
}

/**
 * Calculate MRR events for a subscription based only on its current state
 * This function only uses the subscription document and doesn't rely on historical events
 */
export function calculateMRREvents(
  subscription: Stripe.Subscription
): MRREvent[] {
  const mrrEvents: MRREvent[] = [];
  const metadata = getSubscriptionMetadata(subscription);

  // Create start event based on subscription creation
  const currentMRR = calculateSubscriptionMRR(subscription);
  const startDate = formatEffectiveDate(subscription.created);

  mrrEvents.push({
    uniqueSourceKey: generateUniqueSourceKey(
      subscription.id,
      'start',
      startDate
    ),
    subscriptionId: subscription.id,
    srcEventType: 'start',
    mrrDeltaCents: currentMRR,
    effectiveDate: startDate,
    ...metadata,
  });

  // Create cancel event if subscription is canceled
  if (subscription.status === 'canceled' && subscription.canceled_at) {
    const cancelDate = formatEffectiveDate(subscription.canceled_at);

    mrrEvents.push({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'cancel',
        cancelDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'cancel',
      mrrDeltaCents: -currentMRR,
      effectiveDate: cancelDate,
      ...metadata,
    });
  }

  return mrrEvents;
}

/**
 * Calculate MRR events for a subscription without historical events
 * This is useful for initial sync when we only have the current subscription state
 */
export function calculateMRREventsFromSubscription(
  subscription: Stripe.Subscription
): MRREvent[] {
  const metadata = getSubscriptionMetadata(subscription);
  const mrrEvents: MRREvent[] = [];

  // Create start event
  if (subscription.status !== 'canceled') {
    const currentMRR = calculateSubscriptionMRR(subscription);
    const effectiveDate = formatEffectiveDate(subscription.created);

    mrrEvents.push({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'start',
        effectiveDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'start',
      mrrDeltaCents: currentMRR,
      effectiveDate,
      ...metadata,
    });
  }

  // Create cancel event if subscription is canceled
  if (subscription.status === 'canceled' && subscription.canceled_at) {
    const currentMRR = calculateSubscriptionMRR(subscription);
    const cancelDate = formatEffectiveDate(subscription.canceled_at);

    mrrEvents.push({
      uniqueSourceKey: generateUniqueSourceKey(
        subscription.id,
        'cancel',
        cancelDate
      ),
      subscriptionId: subscription.id,
      srcEventType: 'cancel',
      mrrDeltaCents: -currentMRR,
      effectiveDate: cancelDate,
      ...metadata,
    });
  }

  return mrrEvents;
}
