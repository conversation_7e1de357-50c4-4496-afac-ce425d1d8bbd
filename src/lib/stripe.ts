import { db } from '@/db/client';
import { orgIntegrations } from '@/db/schema';
import { and, eq } from 'drizzle-orm';
import Stripe from 'stripe';

export interface StripeConfig {
  secretKey: string;
  orgId: string;
}

/**
 * Get Stripe client for a specific organization
 */
export async function getStripeClient(orgId: string): Promise<Stripe | null> {
  try {
    // Fetch the Stripe integration for this org
    const integration = await db
      .select()
      .from(orgIntegrations)
      .where(
        and(
          eq(orgIntegrations.orgId, orgId),
          eq(orgIntegrations.provider, 'stripe'),
          eq(orgIntegrations.status, 'connected')
        )
      )
      .limit(1);

    if (!integration.length || !integration[0].secretRef) {
      return null;
    }

    // In a real implementation, you'd fetch the secret from your secrets manager
    // For now, we'll assume the secretRef contains the actual key (not recommended for production)
    const secretKey = integration[0].secretRef;

    return new Stripe(secretKey, {
      apiVersion: '2024-12-18.acacia',
    });
  } catch (error) {
    console.error('Failed to get Stripe client:', error);
    return null;
  }
}

/**
 * Fetch all subscriptions for an organization with pagination
 */
export async function* fetchAllSubscriptions(
  stripe: Stripe,
  options: {
    status?: Stripe.Subscription.Status[];
    limit?: number;
  } = {}
): AsyncGenerator<Stripe.Subscription, void, unknown> {
  const { status, limit = 100 } = options;

  let hasMore = true;
  let startingAfter: string | undefined;

  while (hasMore) {
    const params: Stripe.SubscriptionListParams = {
      limit,
      expand: ['data.items.price.product'],
    };

    if (status) {
      params.status = status.length === 1 ? status[0] : 'all';
    }

    if (startingAfter) {
      params.starting_after = startingAfter;
    }

    const subscriptions = await stripe.subscriptions.list(params);

    for (const subscription of subscriptions.data) {
      // Filter by status if multiple statuses provided
      if (
        status &&
        status.length > 1 &&
        !status.includes(subscription.status)
      ) {
        continue;
      }
      yield subscription;
    }

    hasMore = subscriptions.has_more;
    if (hasMore && subscriptions.data.length > 0) {
      startingAfter = subscriptions.data[subscriptions.data.length - 1].id;
    }
  }
}

/**
 * Fetch subscription with its complete invoice history
 */
export async function fetchSubscriptionWithInvoices(
  stripe: Stripe,
  subscriptionId: string
): Promise<{
  subscription: Stripe.Subscription;
  invoices: Stripe.Invoice[];
}> {
  // Fetch the current subscription
  const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
    expand: ['items.price.product'],
  });

  // Fetch all invoices for this subscription
  const invoices: Stripe.Invoice[] = [];
  let hasMore = true;
  let startingAfter: string | undefined;

  while (hasMore) {
    const params: Stripe.InvoiceListParams = {
      subscription: subscriptionId,
      limit: 100,
      expand: ['data.lines.data.price.product'],
    };

    if (startingAfter) {
      params.starting_after = startingAfter;
    }

    const invoiceList = await stripe.invoices.list(params);
    invoices.push(...invoiceList.data);

    hasMore = invoiceList.has_more;
    if (hasMore && invoiceList.data.length > 0) {
      startingAfter = invoiceList.data[invoiceList.data.length - 1].id;
    }
  }

  // Sort invoices by creation date (oldest first)
  invoices.sort((a, b) => a.created - b.created);

  return {
    subscription,
    invoices,
  };
}

/**
 * Calculate MRR amount from subscription items
 */
export function calculateSubscriptionMRR(
  subscription: Stripe.Subscription
): number {
  let totalMRR = 0;

  for (const item of subscription.items.data) {
    const price = item.price;
    const quantity = item.quantity || 1;

    if (!price.unit_amount) continue;

    let monthlyAmount = price.unit_amount * quantity;

    // Convert to monthly based on interval
    switch (price.recurring?.interval) {
      case 'year':
        monthlyAmount = monthlyAmount / 12;
        break;
      case 'week':
        monthlyAmount = monthlyAmount * 4.33; // Average weeks per month
        break;
      case 'day':
        monthlyAmount = monthlyAmount * 30; // Average days per month
        break;
      case 'month':
      default:
        // Already monthly
        break;
    }

    totalMRR += monthlyAmount;
  }

  return Math.round(totalMRR); // Return in cents
}

/**
 * Get subscription metadata for MRR tracking
 */
export function getSubscriptionMetadata(subscription: Stripe.Subscription) {
  const firstItem = subscription.items.data[0];
  const price = firstItem?.price;
  const product = price?.product as Stripe.Product;

  return {
    customerId: subscription.customer as string,
    productId: typeof product === 'string' ? product : product?.id,
    priceId: price?.id,
  };
}
